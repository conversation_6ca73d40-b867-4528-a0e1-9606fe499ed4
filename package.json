{"name": "next-cloud", "version": "0.1.0", "private": true, "scripts": {"dev": "nodemon", "sandbox": "cross-env env IS_BUILD_ONLINE=true nodemon", "buildOnline": "cross-env env IS_BUILD_ONLINE=true next build && tsc --project tsconfig.server.json", "buildSandbox": "cross-env env IS_BUILD_ONLINE=false next build && tsc --project tsconfig.server.json", "start": "cross-env env NODE_ENV=production IS_BUILD_ONLINE=true node dist/index.js", "lint": "next lint"}, "dependencies": {"@ant-design/icons": "^4.8.0", "@baidu/bce-abtest": "^1.1.16", "@baidu/bce-components": "^1.1.16", "@baidu/bce-decorators": "^1.1.16", "@baidu/bce-helper": "^1.1.16", "@baidu/bce-hooks": "^1.1.16", "@baidu/bce-portal-ui": "^1.0.38", "@baidu/bce-services": "^1.1.16", "@baidu/bce-wx": "1.0.25", "@baidu/im-jssdk": "0.1.39-beta.3", "@baidu/weirwood-sdk": "^1.3.9", "@types/codemirror": "^5.60.15", "@types/react-codemirror": "1.0.8", "acud": "^1.4.53", "acud-icon": "^1.0.8", "antd": "4.24.8", "antd-mobile": "^5.30.0", "axios": "0.21.1", "bce-sdk-js": "^0.2.9", "big.js": "^6.2.1", "classnames": "^2.3.1", "codemirror": "^5.62.3", "core-js": "^3.19.1", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "decimal.js": "^10.4.3", "fabric": "^6.4.2", "framer-motion": "^6.5.1", "highlight.js": "^11.9.0", "js-base64": "^3.7.3", "js-cookie": "^3.0.1", "js-md5": "^0.7.3", "katex": "^0.16.9", "koa": "^2.13.4", "koa-router": "^10.1.1", "lodash": "^4.17.21", "log4js": "^6.4.1", "lottie-react": "^2.4.1", "markdown-it": "^13.0.2", "markdown-it-texmath": "^1.0.0", "markdown-to-jsx": "^7.1.5", "moment": "^2.29.1", "mysql": "^2.18.1", "mysql2": "^2.3.3", "next": "12.3.3", "overlayscrollbars": "^2.10.1", "pdfobject": "^2.3.0", "qrcode.react": "^1.0.1", "qs": "~6.5.2", "react": "17.0.2", "react-app-polyfill": "^2.0.0", "react-codemirror": "1.0.0", "react-codemirror2": "^8.0.0", "react-dom": "17.0.2", "react-infinite-scroll-component": "^6.1.0", "react-infinite-scroller": "^1.2.5", "react-svg": "^16.1.6", "react-syntax-highlighter": "^15.5.0", "regenerator-runtime": "^0.13.9", "sequelize": "3.23.6", "swiper": "^11.1.4", "three": "^0.139.2", "uuid": "^8.3.2", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "7.12.10", "@babel/eslint-parser": "7.12.1", "@babel/eslint-plugin": "7.12.1", "@baidu/weirwood-webpack-plugin": "^0.0.50", "@ecomfe/eslint-config": "^7.4.0", "@types/big.js": "^6.1.6", "@types/js-cookie": "^3.0.1", "@types/js-md5": "^0.4.3", "@types/katex": "^0.16.6", "@types/koa": "^2.13.4", "@types/koa-router": "^7.4.4", "@types/lodash": "^4.14.189", "@types/markdown-it": "^13.0.6", "@types/node": "20.11.19", "@types/pdfobject": "^2.2.5", "@types/qs": "6.9.7", "@types/react": "17.0.30", "@types/react-dom": "^18.0.9", "@types/react-infinite-scroller": "^1.2.2", "@types/react-syntax-highlighter": "^15.5.10", "@types/sequelize": "^4.28.14", "@types/three": "^0.139.0", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "cross-env": "^7.0.3", "eslint": "7.24.0", "eslint-config-next": "11.1.2", "eslint-plugin-import": "2.22.1", "eslint-plugin-react": "7.23.2", "eslint-plugin-react-hooks": "4.2.0", "eslint-webpack-plugin": "^3.0.1", "file-loader": "^6.2.0", "less": "^4.1.2", "less-loader": "^10.1.0", "next-compose-plugins": "^2.2.1", "next-plugin-antd-less": "^1.8.0", "next-transpile-modules": "^9.1.0", "next-with-less": "^2.0.5", "nodemon": "^2.0.13", "postcss-pxtorem": "^6.0.0", "sass": "^1.43.2", "swc-plugin-another-transform-imports": "0.1.5", "ts-node": "^10.3.0", "typescript": "4.4.4", "url-loader": "^4.1.1"}, "browserslist": {"production": ["> 0.2%", "last 10 version", "not ie <= 8"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}